package xiaozhi.modules.contact.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xiaozhi.modules.contact.entity.ContactEntity;

import java.util.List;

/**
 * 通讯录数据访问层
 */
@Mapper
public interface ContactDao extends BaseMapper<ContactEntity> {

    /**
     * 根据用户ID查询通讯录列表
     *
     * @param userId 用户ID
     * @return 通讯录列表
     */
    List<ContactEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和邮箱查询通讯录
     *
     * @param userId 用户ID
     * @param email 邮箱
     * @return 通讯录实体
     */
    ContactEntity selectByUserIdAndEmail(@Param("userId") Long userId, @Param("email") String email);
}
