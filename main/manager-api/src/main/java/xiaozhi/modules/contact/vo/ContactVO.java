package xiaozhi.modules.contact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通讯录展示VO
 */
@Data
@Schema(description = "通讯录信息")
public class ContactVO implements Serializable {

    @Schema(description = "通讯录ID")
    private String id;

    @Schema(description = "称呼")
    private String name;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "创建时间")
    private Date createDate;

    @Schema(description = "更新时间")
    private Date updateDate;
}
