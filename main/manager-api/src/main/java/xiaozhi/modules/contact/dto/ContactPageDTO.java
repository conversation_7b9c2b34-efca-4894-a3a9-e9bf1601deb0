package xiaozhi.modules.contact.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通讯录分页查询DTO
 */
@Data
@Schema(description = "通讯录分页查询")
public class ContactPageDTO {

    @Schema(description = "页码")
    private String page;

    @Schema(description = "每页数量")
    private String limit;

    @Schema(description = "称呼（模糊查询）")
    private String name;

    @Schema(description = "邮箱（模糊查询）")
    private String email;
}
