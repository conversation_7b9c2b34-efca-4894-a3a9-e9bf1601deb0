package xiaozhi.modules.contact.service;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.contact.dto.ContactPageDTO;
import xiaozhi.modules.contact.dto.ContactSaveDTO;
import xiaozhi.modules.contact.dto.ContactUpdateDTO;
import xiaozhi.modules.contact.entity.ContactEntity;
import xiaozhi.modules.contact.vo.ContactVO;

import java.util.List;

/**
 * 通讯录服务接口
 */
public interface ContactService extends BaseService<ContactEntity> {

    /**
     * 分页查询通讯录
     *
     * @param dto 分页查询参数
     * @param userId 用户ID
     * @return 分页数据
     */
    PageData<ContactVO> page(ContactPageDTO dto, Long userId);

    /**
     * 根据ID获取通讯录详情
     *
     * @param id 通讯录ID
     * @param userId 用户ID
     * @return 通讯录详情
     */
    ContactVO get(String id, Long userId);

    /**
     * 保存通讯录
     *
     * @param dto 保存参数
     * @param userId 用户ID
     */
    void save(ContactSaveDTO dto, Long userId);

    /**
     * 更新通讯录
     *
     * @param id 通讯录ID
     * @param dto 更新参数
     * @param userId 用户ID
     */
    void update(String id, ContactUpdateDTO dto, Long userId);

    /**
     * 删除通讯录
     *
     * @param ids 通讯录ID数组
     * @param userId 用户ID
     */
    void delete(String[] ids, Long userId);

    /**
     * 获取用户的所有通讯录
     *
     * @param userId 用户ID
     * @return 通讯录列表
     */
    List<ContactVO> getUserContacts(Long userId);
}
