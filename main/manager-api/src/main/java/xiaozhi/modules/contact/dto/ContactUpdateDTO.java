package xiaozhi.modules.contact.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 通讯录更新DTO
 */
@Data
@Schema(description = "通讯录更新")
public class ContactUpdateDTO {

    @Schema(description = "称呼", required = true)
    @NotBlank(message = "称呼不能为空")
    private String name;

    @Schema(description = "邮箱地址", required = true)
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
}
