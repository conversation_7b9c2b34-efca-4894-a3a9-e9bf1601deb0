package xiaozhi.modules.contact.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xiaozhi.common.page.PageData;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.agent.dto.McpcnUserInfoResponseDTO;
import xiaozhi.modules.agent.service.McpcnApiService;
import xiaozhi.modules.contact.dto.ContactPageDTO;
import xiaozhi.modules.contact.dto.ContactSaveDTO;
import xiaozhi.modules.contact.dto.ContactUpdateDTO;
import xiaozhi.modules.contact.service.ContactService;
import xiaozhi.modules.contact.vo.ContactVO;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.timbre.entity.TimbreEntity;

import java.util.List;
import java.util.Map;

/**
 * 通讯录管理
 */
@Tag(name = "通讯录管理")
@AllArgsConstructor
@RestController
@RequestMapping("/contact")
public class ContactController {

    private final ContactService contactService;

    @Autowired
    private McpcnApiService mcpcnApiService;

    @GetMapping("/page")
    @Operation(summary = "分页查询通讯录")
    @RequiresPermissions("sys:role:normal")
    public Result<PageData<ContactVO>> page(
            @Parameter(hidden = true) @RequestParam Map<String, Object> params, HttpServletRequest request) {
        
        ContactPageDTO dto = new ContactPageDTO();
        dto.setPage((String) params.get("page"));
        dto.setLimit((String) params.get("limit"));
        dto.setName((String) params.get("name"));
        dto.setEmail((String) params.get("email"));

        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<PageData<ContactVO>>().error(7, userInfoResponse.getMsg());
        }

        PageData<ContactVO> page = contactService.page(dto, userInfoResponse.getData().getUserInfo().getId());
        return new Result<PageData<ContactVO>>().ok(page);
    }

    @GetMapping("/list")
    @Operation(summary = "获取用户所有通讯录")
    public Result<List<ContactVO>> list(HttpServletRequest request) {
        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<List<ContactVO>>().error(7, userInfoResponse.getMsg());
        }

        List<ContactVO> list = contactService.getUserContacts(userInfoResponse.getData().getUserInfo().getId());
        return new Result<List<ContactVO>>().ok(list);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取通讯录详情")
    public Result<ContactVO> get(@PathVariable("id") String id, HttpServletRequest request) {

        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<ContactVO>().error(7, userInfoResponse.getMsg());
        }

        ContactVO contact = contactService.get(id, userInfoResponse.getData().getUserInfo().getId());
        if (contact == null) {
            return new Result<ContactVO>().error("通讯录不存在");
        }
        return new Result<ContactVO>().ok(contact);
    }

    @PostMapping
    @Operation(summary = "新增通讯录")
    public Result<Void> save(@RequestBody @Valid ContactSaveDTO dto, HttpServletRequest request) {
        ValidatorUtils.validateEntity(dto);
        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<Void>().error(7, userInfoResponse.getMsg());
        }

        contactService.save(dto, userInfoResponse.getData().getUserInfo().getId());
        return new Result<>();
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新通讯录")
    public Result<Void> update(@PathVariable("id") String id, @RequestBody @Valid ContactUpdateDTO dto, HttpServletRequest request) {
        ValidatorUtils.validateEntity(dto);
        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<Void>().error(7, userInfoResponse.getMsg());
        }

        contactService.update(id, dto, userInfoResponse.getData().getUserInfo().getId());
        return new Result<>();
    }

    @DeleteMapping
    @Operation(summary = "删除通讯录")
    public Result<Void> delete(@RequestBody String[] ids, HttpServletRequest request) {
        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<Void>().error(7, userInfoResponse.getMsg());
        }

        contactService.delete(ids, userInfoResponse.getData().getUserInfo().getId());
        return new Result<>();
    }
}
