package xiaozhi.modules.contact.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.contact.dao.ContactDao;
import xiaozhi.modules.contact.dto.ContactPageDTO;
import xiaozhi.modules.contact.dto.ContactSaveDTO;
import xiaozhi.modules.contact.dto.ContactUpdateDTO;
import xiaozhi.modules.contact.entity.ContactEntity;
import xiaozhi.modules.contact.service.ContactService;
import xiaozhi.modules.contact.vo.ContactVO;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通讯录服务实现类
 */
@Service
@AllArgsConstructor
public class ContactServiceImpl extends BaseServiceImpl<ContactDao, ContactEntity> implements ContactService {

    private final ContactDao contactDao;

    @Override
    public PageData<ContactVO> page(ContactPageDTO dto, Long userId) {
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());

        QueryWrapper<ContactEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        
        if (StringUtils.isNotBlank(dto.getName())) {
            wrapper.like("name", dto.getName());
        }
        
        if (StringUtils.isNotBlank(dto.getEmail())) {
            wrapper.like("email", dto.getEmail());
        }
        
        wrapper.orderByDesc("create_date");

        IPage<ContactEntity> page = baseDao.selectPage(getPage(params, null, false), wrapper);
        return getPageData(page, ContactVO.class);
    }

    @Override
    public ContactVO get(String id, Long userId) {
        ContactEntity entity = baseDao.selectById(id);
        if (entity == null || !entity.getUserId().equals(userId)) {
            return null;
        }
        return ConvertUtils.sourceToTarget(entity, ContactVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ContactSaveDTO dto, Long userId) {
        // 检查邮箱是否已存在
        ContactEntity existContact = contactDao.selectByUserIdAndEmail(userId, dto.getEmail());
        if (existContact != null) {
            throw new RenException("该邮箱已存在于通讯录中");
        }

        ContactEntity entity = ConvertUtils.sourceToTarget(dto, ContactEntity.class);
        entity.setUserId(userId);
        baseDao.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ContactUpdateDTO dto, Long userId) {
        ContactEntity entity = baseDao.selectById(id);
        if (entity == null || !entity.getUserId().equals(userId)) {
            throw new RenException("通讯录不存在");
        }

        // 检查邮箱是否已被其他联系人使用
        ContactEntity existContact = contactDao.selectByUserIdAndEmail(userId, dto.getEmail());
        if (existContact != null && !existContact.getId().equals(id)) {
            throw new RenException("该邮箱已存在于通讯录中");
        }

        entity.setName(dto.getName());
        entity.setEmail(dto.getEmail());
        baseDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String[] ids, Long userId) {
        for (String id : ids) {
            ContactEntity entity = baseDao.selectById(id);
            if (entity != null && entity.getUserId().equals(userId)) {
                baseDao.deleteById(id);
            }
        }
    }

    @Override
    public List<ContactVO> getUserContacts(Long userId) {
        List<ContactEntity> entities = contactDao.selectByUserId(userId);
        return ConvertUtils.sourceToTarget(entities, ContactVO.class);
    }
}
