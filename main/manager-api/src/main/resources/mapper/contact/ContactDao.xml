<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.contact.dao.ContactDao">

    <!-- 根据用户ID查询通讯录列表 -->
    <select id="selectByUserId" resultType="xiaozhi.modules.contact.entity.ContactEntity">
        SELECT * FROM contact 
        WHERE user_id = #{userId}
        ORDER BY create_date DESC
    </select>

    <!-- 根据用户ID和邮箱查询通讯录 -->
    <select id="selectByUserIdAndEmail" resultType="xiaozhi.modules.contact.entity.ContactEntity">
        SELECT * FROM contact 
        WHERE user_id = #{userId} AND email = #{email}
        LIMIT 1
    </select>

</mapper>
