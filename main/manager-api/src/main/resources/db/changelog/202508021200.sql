-- 创建通讯录表
DROP TABLE IF EXISTS `contact`;
CREATE TABLE `contact` (
    `id` VARCHAR(32) NOT NULL COMMENT '通讯录唯一标识',
    `user_id` BIGINT COMMENT '关联用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '称呼',
    `email` VARCHAR(255) NOT NULL COMMENT '邮箱地址',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_contact_user_id` (`user_id`) COMMENT '用户ID索引',
    INDEX `idx_contact_email` (`email`) COMMENT '邮箱索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通讯录表';
